/* eslint-disable i18next/no-literal-string */
import { Button, IListItem, Input, MultiSelect } from '@/shared/ui'
import styles from './phishing-campaigns.module.scss'
import classNamesBind from 'classnames/bind'
import { useTranslation } from 'react-i18next'
import { Modal } from '@/shared/components'
import { PhishingCampaignsState } from './slice'
import { SelectDateCardWithInput } from '@/shared/components/SelectDateCard/select-date-card-with-input'
import { useGetTranslatePhishingTypeList } from '@/shared/hooks'
import { useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { isNumber } from '@/shared/helpers'
import { zodResolver } from '@hookform/resolvers/zod'
import { getPhishingCampaignsFiltersSchema } from './resolver'
import { getRangeValue } from './helpers'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'pages__phishing-campaigns'

export type PhishingCampaignsStateForm = Omit<
  PhishingCampaignsState['filters'],
  'start_date_from' | 'start_date_to' | 'end_date_from' | 'end_date_to'
> & {
  start_date_from: Date | null
  start_date_to: Date | null
  end_date_from: Date | null
  end_date_to: Date | null
}

type PhishingCampaignsFiltersProps = PhishingCampaingsFiltersModalProps & {
  onSubmit: (data: PhishingCampaignsStateForm) => void
  hasFilter: boolean
  isModalOpen: boolean
  filters: PhishingCampaignsState['filters']
  resetFilters: () => void
  onModalOpen: (v: boolean) => void
}

export const PhishingCampaignsFilters = (props: PhishingCampaignsFiltersProps) => {
  const { t } = useTranslation(TRANSLATION_FILE)

  return (
    <div className={cx('filters-wrapper')}>
      {props.hasFilter && (
        <Button color='gray' size='small' onClick={props.resetFilters}>
          {t('commons:reset')}
        </Button>
      )}
      <Button
        color={'gray'}
        size='small'
        rightIcon='filter'
        className={cx('filterButton', {
          active: props.hasFilter,
        })}
        onClick={() => props.onModalOpen(true)}
      >
        {t('commons:filters')}
      </Button>
      {props.isModalOpen && <PhishingCampaingsFiltersModal {...props} />}
    </div>
  )
}

type PhishingCampaingsFiltersModalProps = {
  onSubmit: (data: PhishingCampaignsStateForm) => void
  isModalOpen: boolean
  filters: PhishingCampaignsState['filters']
  onModalOpen: (v: boolean) => void
  resetFilters: () => void
}

export const PhishingCampaingsFiltersModal = (props: PhishingCampaingsFiltersModalProps) => {
  const { t } = useTranslation(TRANSLATION_FILE)
  const PHISHING_TYPE_LIST = useGetTranslatePhishingTypeList()

  const resolver = useMemo(
    () =>
      zodResolver(
        getPhishingCampaignsFiltersSchema({
          end_date_to_message: t('end_date_to_message'),
          incident_percent_to_message: t('incident_percent_to_message'),
          start_date_to_message: t('start_date_to_message'),
          targets_to_message: t('targets_to_message'),
        }),
      ),
    [t],
  )
  const form = useForm<PhishingCampaignsStateForm>({
    defaultValues: {
      ...props?.filters,
      start_date_from: props?.filters.start_date_from
        ? new Date(props?.filters.start_date_from)
        : null,
      start_date_to: props?.filters.start_date_to ? new Date(props?.filters.start_date_to) : null,
      end_date_from: props?.filters.end_date_from ? new Date(props?.filters.end_date_from) : null,
      end_date_to: props?.filters.end_date_to ? new Date(props?.filters.end_date_to) : null,
    },
    mode: 'all',
    resolver,
  })

  const incidentPercentFromValue = isNumber(form.watch('incident_percent_from'))
    ? String(form.watch('incident_percent_from'))
    : ''
  const incidentPercentToValue = isNumber(form.watch('incident_percent_to'))
    ? String(form.watch('incident_percent_to'))
    : ''

  const onSubmit = form.handleSubmit(data => {
    props.onSubmit(data)
  })

  const getStartDateErrors = () => {
    if (form.formState.errors.start_date_from) {
      return form.formState.errors.start_date_from.message
    }
    if (form.formState.errors.start_date_to) {
      return form.formState.errors.start_date_to.message
    }
    return ''
  }

  const getIncidentErrors = () => {
    if (form.formState.errors.incident_percent_from) {
      return form.formState.errors.incident_percent_from.message
    }
    if (form.formState.errors.incident_percent_to) {
      return form.formState.errors.incident_percent_to.message
    }
    return ''
  }

  const getTargetsErrors = () => {
    if (form.formState.errors.targets_from) {
      return form.formState.errors.targets_from.message
    }
    if (form.formState.errors.targets_to) {
      return form.formState.errors.targets_to.message
    }
    return ''
  }

  return (
    <Modal
      active={props.isModalOpen}
      setActive={v => props.onModalOpen(!!v)}
      className={cx('modal')}
    >
      <h3 className={cx('modal__title')}>{t('modal_title')}</h3>
      <form className={cx('modal__form')} onSubmit={onSubmit}>
        <div className={cx('modal__start')}>
          <div className={cx('modal__intervals')}>
            <SelectDateCardWithInput
              label={t('start_date_from')}
              wrapperClassName={cx('modal__interval')}
              datePickerClassName={cx('modal__datepicker')}
              classNameLabel={cx('modal__interval-label')}
              onChange={v => {
                const date_start = v ?? null
                if (!date_start) return
                form.setValue('start_date_from', date_start, { shouldDirty: true })
              }}
              withoutTime
              selected={form.watch('start_date_from') ?? null}
              text={t('commons:choose')}
              min={null}
            />
            <div className={cx('modal__separator')}>:</div>
            <SelectDateCardWithInput
              label={t('start_date_to')}
              wrapperClassName={cx('modal__interval')}
              datePickerClassName={cx('modal__datepicker')}
              classNameLabel={cx('modal__interval-label')}
              onChange={v => {
                const date_end = v ?? null
                if (!date_end) return
                form.setValue('start_date_to', date_end, { shouldDirty: true })
              }}
              withoutTime
              selected={form.watch('start_date_to') ?? null}
              text={t('commons:choose')}
              min={form.watch('start_date_from') ?? null}
            />
          </div>
          {getStartDateErrors() && <p>{getStartDateErrors()}</p>}
        </div>
        <div className={cx('modal__intervals')}>
          <SelectDateCardWithInput
            label={t('end_date_from')}
            wrapperClassName={cx('modal__interval')}
            datePickerClassName={cx('modal__datepicker')}
            classNameLabel={cx('modal__interval-label')}
            onChange={v => {
              const date_start = v ?? null
              if (!date_start) return
              form.setValue('end_date_from', date_start, { shouldDirty: true })
            }}
            withoutTime
            selected={form.watch('end_date_from') ?? null}
            text={t('commons:choose')}
            min={null}
          />
          <div className={cx('modal__separator')}>:</div>
          <SelectDateCardWithInput
            label={t('end_date_to')}
            wrapperClassName={cx('modal__interval')}
            datePickerClassName={cx('modal__datepicker')}
            classNameLabel={cx('modal__interval-label')}
            onChange={v => {
              const date_end = v ?? null
              if (!date_end) return
              form.setValue('end_date_to', date_end, { shouldDirty: true })
            }}
            withoutTime
            selected={form.watch('end_date_to') ?? null}
            text={t('commons:choose')}
            min={form.watch('end_date_from') ?? null}
          />
        </div>
        <div className={cx('modal__incident')}>
          <div className={cx('modal__intervals')}>
            <Input
              label={t('incident_percent_from')}
              labelClassName={cx('modal__interval-label', 'fullWidth')}
              classNameWrapper={cx('fullWidth')}
              fullWidth
              value={incidentPercentFromValue}
              onChange={v => {
                const percentFrom = Number(v)
                if (Number.isNaN(percentFrom)) {
                  return
                }
                form.setValue(
                  'incident_percent_from',
                  getRangeValue({
                    value: percentFrom,
                    min: 0,
                    max: 100,
                  }),
                  { shouldValidate: true },
                )
                // eslint-disable-next-line i18next/no-literal-string
                form.trigger('incident_percent_to')
              }}
              max={100}
            />
            <div className={cx('modal__separator')}> - </div>
            <Input
              label={t('incident_percent_to')}
              labelClassName={cx('modal__interval-label', 'fullWidth')}
              fullWidth
              classNameWrapper={cx('fullWidth')}
              value={incidentPercentToValue}
              onChange={v => {
                const percentTo = Number(v)
                if (Number.isNaN(percentTo)) {
                  return
                }
                form.setValue(
                  'incident_percent_to',
                  getRangeValue({
                    value: percentTo,
                    min: 0,
                    max: 100,
                  }),
                  { shouldValidate: true },
                )
              }}
              min={form.watch('incident_percent_from') ?? ''}
              max={100}
            />
          </div>
          {getIncidentErrors() && <p className={cx('error')}>{getIncidentErrors()}</p>}
        </div>
        <div>
          <div className={cx('modal__intervals', 'modal__targets')}>
            <Input
              label={t('targets_from')}
              labelClassName={cx('modal__interval-label', 'fullWidth')}
              type='number'
              value={String(form.watch('targets_from'))}
              classNameWrapper={cx('fullWidth')}
              fullWidth
              onChange={v => {
                const targetsFrom = Number(v)
                form.setValue('targets_from', targetsFrom, { shouldValidate: true })
                form.trigger('targets_to')
              }}
            />
            <div className={cx('modal__separator')}> - </div>
            <Input
              label={t('targets_to')}
              labelClassName={cx('modal__interval-label', 'fullWidth')}
              fullWidth
              classNameWrapper={cx('fullWidth')}
              type='number'
              value={String(form.watch('targets_to'))}
              onChange={v => {
                const targetsTo = Number(v)
                form.setValue('targets_to', targetsTo, { shouldValidate: true })
              }}
              min={form.watch('targets_from') ?? 0}
            />
          </div>
          {getTargetsErrors() && <p className={cx('error')}>{getTargetsErrors()}</p>}
        </div>
        <div className={cx('modal__intervals', 'modal__phishing')}>
          <MultiSelect
            label={t('commons:status')}
            placeholder={t('commons:not_important')}
            onChange={(v: IListItem) => {
              const events = form.watch('phishing_events') ?? []
              if (events.includes(v?.id)) {
                const newEvents = events.filter(e => e !== v.id)
                form.setValue('phishing_events', newEvents, { shouldDirty: true })
              } else {
                form.setValue('phishing_events', [...events, v?.id], { shouldDirty: true })
              }
            }}
            list={PHISHING_TYPE_LIST}
            customValue={form.watch('phishing_events') ?? []}
          />
        </div>
        <div className={cx('modal__buttons')}>
          <Button
            size='big'
            color='gray'
            type='button'
            onClick={() => {
              props.onModalOpen(false)
              props.resetFilters()
            }}
          >
            {t('commons:cancel')}
          </Button>
          <Button disabled={!form.formState.isValid} type='submit' size='big' color='green'>
            {t('commons:apply')}
          </Button>
        </div>
      </form>
    </Modal>
  )
}
