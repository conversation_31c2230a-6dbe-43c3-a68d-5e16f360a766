import { createSlice, PayloadAction, WithSlice } from '@reduxjs/toolkit'
import { rootReducer } from '@/store/reducer'
import { PhishingCampaignsFilters } from '@/entities/phishing/model/types'

export type PhishingCampaignsState = {
  filters: PhishingCampaignsFilters
  limit: number
  offset: number

  autoPhishConfirmOpen: boolean
  filterModalOpen: boolean
  hasFilters: boolean
}

export const initialFilters: PhishingCampaignsState['filters'] = {
  search: null,
  status: 'active',
  organization_id: null,
  by_tag: true,
  sort_by: 'START_DATE',
  sort_order: 'desc',

  start_date_from: null,
  start_date_to: null,
  end_date_from: null,
  end_date_to: null,

  incident_percent_from: 0,
  incident_percent_to: 100,

  targets_from: null,
  targets_to: null,

  templates_ids: [],
  phishing_events: [],
}

const initialState: PhishingCampaignsState = {
  filters: initialFilters,
  limit: 20,
  offset: 0,
  autoPhishConfirmOpen: false,
  filterModalOpen: false,
  hasFilters: false,
}

export const phishingCampaignsByTagSlice = createSlice({
  name: 'phishingCampaignsByTagSlice',
  initialState,
  reducers: {
    setLimit: (state, action: PayloadAction<number>) => {
      state.limit = action.payload
    },
    setOffset: (state, action: PayloadAction<number>) => {
      state.offset = action.payload
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.offset = action.payload * state.limit
    },
    setFilters: (state, action: PayloadAction<Partial<PhishingCampaignsFilters>>) => {
      state.filters = { ...state.filters, ...action.payload }
    },
    resetFilters: state => {
      state.filters = { ...initialState.filters, status: state.filters.status }
      state.offset = 0
    },
    setAutoPhishConfirmOpen: (state, action: PayloadAction<boolean>) => {
      state.autoPhishConfirmOpen = action.payload
    },
    setFilterModalOpen: (state, action: PayloadAction<boolean>) => {
      state.filterModalOpen = action.payload
    },
  },
  selectors: {
    selectPhishingCampaigns: s => s,
    selectPagination: s => ({ limit: s.limit, offset: s.offset }),
    selectFilters: s => s.filters,
    selectSorting: s => ({ sort_by: s.filters.sort_by, sort_order: s.filters.sort_order }),
    selectAutoPhishConfirmOpen: s => s.autoPhishConfirmOpen,
    selectFilterModalOpen: s => s.filterModalOpen,
    selectHasFilters: s => {
      const filters = s.filters

      return (
        filters.organization_id !== null ||
        filters.start_date_from !== null ||
        filters.start_date_to !== null ||
        filters.end_date_from !== null ||
        filters.end_date_to !== null ||
        (filters.incident_percent_from !== null && filters.incident_percent_from !== 0) ||
        (filters.incident_percent_to !== null && filters.incident_percent_to !== 100) ||
        filters.targets_from !== null ||
        filters.targets_to !== null ||
        (filters.templates_ids && filters.templates_ids.length > 0) ||
        (filters.phishing_events && filters.phishing_events.length > 0)
      )
    },
  },
})

declare module '@/store/reducer' {
  export interface LazyLoadedSlices extends WithSlice<typeof phishingCampaignsByTagSlice> {}
}

const injectedSlice = phishingCampaignsByTagSlice.injectInto(rootReducer, {
  overrideExisting: true,
  reducerPath: 'phishingCampaignsByTagSlice',
})

export const phishingCampaignsByTagActions = injectedSlice.actions
export const phishingCampaignsByTagSelectors = injectedSlice.selectors
