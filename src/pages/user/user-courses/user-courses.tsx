import { FC, useEffect, useMemo, useRef, useState } from 'react'
import styles from './user-courses.module.scss'
import classNamesBind from 'classnames/bind'
import { MyCoursesProps } from './user-courses.d'
import { <PERSON><PERSON>, Loader, PageTitle } from '@/shared/ui'
import { CertificatesModal } from '@/shared/modals/certificates-modal'
import { useMyCourses } from './use-user-courses'
import { useTranslation } from 'react-i18next'
import { Link, useSearchParams } from 'react-router-dom'
import {
  CourseCard,
  CourseStatistics,
  isCourseByTag,
  MyCoursesData,
  myCoursesQueryApi,
  MyCoursesStatus,
} from '@/entities/courses'
import { sharedCoursesApi } from '@/entities/courses/model/api/shared'
import type { MySharedCourse } from '@/entities/courses/model/types/courses'
import { Tabs } from '@/shared/components'

type CombinedCourseData = MyCoursesData | MySharedCourse
import { LoadingDataStatus } from '@/shared/components/loading-data-status'
import { getMyCoursesLearningByIdUrl } from '@/shared/configs/urls'
import InfiniteScroll from 'react-infinite-scroll-component'
import { useAnalytics } from '@/shared/hooks/use-analytics'
import { GOALS } from '@/shared/constants'

const cx = classNamesBind.bind(styles)

type MyCourseTabVariants = MyCoursesStatus

const LIMIT = 12

export const MyCourses: FC<MyCoursesProps.Props> = props => {
  const { className } = props
  const { t } = useTranslation()

  const [searchParams, setSearchParams] = useSearchParams()
  const tabValue: MyCourseTabVariants = (searchParams.get('tab') as MyCourseTabVariants) || 'active'

  const [page, setPage] = useState(1)
  const [totalCoursesCount, setTotalCoursesCount] = useState(0)
  const [renderCourses, setRenderCourses] = useState<MyCoursesData[] | null>(null)

  const analytics = useAnalytics()

  const {
    data: myCourses,
    status,
    isLoading: isMyCoursesLoading,
    isFetching: isMyCoursesFetching,
  } = myCoursesQueryApi.useGetMyCoursesQuery(
    {
      limit: LIMIT,
      status: tabValue as MyCoursesStatus,
      offset: (page - 1) * LIMIT,
    },
    {
      skip: tabValue === 'shared',
    },
  )

  const {
    data: mySharedCourses,
    status: sharedStatus,
    isLoading: isMySharedCoursesLoading,
    isFetching: isMySharedCoursesFetching,
  } = sharedCoursesApi.useGetMySharedCoursesQuery(
    {
      limit: LIMIT,
      offset: (page - 1) * LIMIT,
      status: 'active',
    },
    {
      skip: tabValue !== 'shared',
    },
  )

  const {
    data: myCompletedSharedCourses,
    isLoading: isMyCompletedSharedCoursesLoading,
    isFetching: isMyCompletedSharedCoursesFetching,
  } = sharedCoursesApi.useGetMySharedCoursesQuery(
    {
      limit: LIMIT,
      offset: (page - 1) * LIMIT,
      status: 'completed',
    },
    {
      skip: tabValue !== 'completed',
    },
  )

  const combinedCompletedCourses = useMemo(() => {
    if (tabValue !== 'completed') return null

    const assignedData = myCourses?.data || []
    const sharedData = myCompletedSharedCourses?.data || []

    return {
      data: [...assignedData, ...sharedData],
      total_count: (myCourses?.total_count || 0) + (myCompletedSharedCourses?.total_count || 0),
    }
  }, [tabValue, myCourses, myCompletedSharedCourses])

  const currentData = useMemo(() => {
    if (tabValue === 'shared') {
      return mySharedCourses
    } else if (tabValue === 'completed') {
      return combinedCompletedCourses
    }
    return myCourses
  }, [tabValue, mySharedCourses, myCourses, combinedCompletedCourses])

  const isCurrentLoading =
    tabValue === 'shared'
      ? isMySharedCoursesLoading
      : tabValue === 'completed'
        ? isMyCoursesLoading || isMyCompletedSharedCoursesLoading
        : isMyCoursesLoading

  const isCurrentFetching =
    tabValue === 'shared'
      ? isMySharedCoursesFetching
      : tabValue === 'completed'
        ? isMyCoursesFetching || isMyCompletedSharedCoursesFetching
        : isMyCoursesFetching

  const sentedEvent = useRef(false) // Чтобы не отправлялся ивент много раз и useRef, чтобы не ререндерилось

  useEffect(() => {
    if (sentedEvent.current) return

    analytics.event(GOALS['courses-page'].name)
    sentedEvent.current = true
  }, [analytics])

  useEffect(() => {
    if (currentData?.data && !(isCurrentLoading || isCurrentFetching)) {
      setRenderCourses(prevCourses => {
        if (!prevCourses) return [...currentData.data]
        const uniqueIds: { [key: string]: boolean } = {}

        return [...prevCourses, ...currentData.data].filter(item => {
          const courseId = item.assigned_course.id

          if (uniqueIds[courseId]) {
            return false
          } else {
            uniqueIds[courseId] = true
            return true
          }
        })
      })

      setTotalCoursesCount(currentData.total_count)
    }
  }, [isCurrentFetching, isCurrentLoading, currentData, tabValue])

  const { onModalClick, activeModal, setActiveModal } = useMyCourses()

  const tabs = useMemo<{ value: string; name: MyCourseTabVariants }[]>(
    () => [
      {
        value: t('commons:active'),
        name: 'active',
      },
      {
        value: t('commons:completed'),
        name: 'completed',
      },
      {
        value: t('commons:shared'),
        name: 'shared',
      },
    ],
    [t],
  )

  const handleTabClick = (tab: string) => {
    if (!tab || tab === tabValue) return
    const newQueryParameters: URLSearchParams = new URLSearchParams()

    newQueryParameters.set('tab', tab)
    setPage(1)
    setRenderCourses(null)
    setSearchParams(newQueryParameters)
  }

  const handleCourseClick = (course: MyCoursesData) => {
    analytics.event(GOALS['courses-choose'].name, course)
  }

  const isCanLoadMore = !!renderCourses && renderCourses.length < totalCoursesCount

  return (
    <div className={cx('wrapper', className)}>
      <div className={cx('title-wrapper')}>
        <PageTitle className={cx('title')}>{t('commons:my_courses')}</PageTitle>
        <Button onClick={onModalClick} className={cx('getCert')}>
          {t('commons:cert_list')}
        </Button>
      </div>
      <Tabs
        tabs={tabs}
        onClick={handleTabClick}
        active={tabValue}
        tabClassname={cx('tabs__item')}
        className={cx('tabs')}
      />
      <LoadingDataStatus
        data={currentData?.data}
        fetchStatus={tabValue === 'shared' ? sharedStatus : status}
        createHandler={() => {}}
        dataLength={currentData?.data?.length}
        showCreateHandler={false}
      />

      {tabValue === 'active' && renderCourses && (
        <InfiniteScroll
          className={cx('content')}
          dataLength={renderCourses?.length}
          next={() => setPage(prevPage => prevPage + 1)}
          hasMore={isCanLoadMore}
          loader={<Loader size='56' loading />}
          scrollableTarget={'page-wrapper'}
        >
          <ActiveCourses courses={renderCourses} handleCourseClick={handleCourseClick} />
        </InfiniteScroll>
      )}

      {tabValue === 'completed' && renderCourses && (
        <InfiniteScroll
          className={cx('content')}
          dataLength={renderCourses?.length}
          next={() => setPage(prevPage => prevPage + 1)}
          hasMore={isCanLoadMore}
          loader={<Loader size='56' loading />}
          scrollableTarget={'page-wrapper'}
        >
          <CompletedCourses courses={renderCourses} />
        </InfiniteScroll>
      )}

      {tabValue === 'shared' && renderCourses && (
        <InfiniteScroll
          className={cx('content')}
          dataLength={renderCourses?.length}
          next={() => setPage(prevPage => prevPage + 1)}
          hasMore={isCanLoadMore}
          loader={<Loader size='56' loading />}
          scrollableTarget={'page-wrapper'}
        >
          <ActiveCourses courses={renderCourses} handleCourseClick={handleCourseClick} />
        </InfiniteScroll>
      )}

      {activeModal && <CertificatesModal active={activeModal} setActive={setActiveModal} />}
    </div>
  )
}

const ActiveCourses = (props: {
  courses: MyCoursesData[] | null
  handleCourseClick: (course: MyCoursesData) => void
}) => {
  const { courses, handleCourseClick } = props

  return (
    <div className={cx('courses__wrapper')}>
      {courses &&
        courses.length > 0 &&
        courses.map(course => {
          const { assigned_course, statistics } = course
          const isCompleted = statistics.overall === 100

          return (
            <Link
              className={cx(isCompleted && 'courses_completed')}
              key={assigned_course.id}
              to={getMyCoursesLearningByIdUrl(assigned_course.id)}
              onClick={() => handleCourseClick(course)}
            >
              <CourseCard
                title={assigned_course?.title}
                imageUrl={assigned_course.picture}
                className={cx('card')}
                BottomAdornment={() => {
                  const isCompleted = statistics?.overall >= 100
                  const isTheoryNotExists = isCompleted && statistics.theory === 0
                  const isQuizNotExists = isCompleted && statistics.quiz === 0

                  const isByTag = assigned_course?.end_date
                    ? isCourseByTag(new Date(assigned_course?.end_date))
                    : false

                  return (
                    <CourseStatistics
                      start_date={!isByTag ? new Date().toString() : undefined}
                      end_date={!isByTag ? assigned_course?.end_date : undefined}
                      testing={isQuizNotExists ? undefined : statistics.quiz}
                      themes={assigned_course.themes_count}
                      theory={isTheoryNotExists ? undefined : statistics.theory}
                      isCompleted={isCompleted}
                    />
                  )
                }}
              />
            </Link>
          )
        })}
    </div>
  )
}

const CompletedCourses = ({ courses }: { courses: CombinedCourseData[] | null }) => {
  return (
    <div className={cx('courses__wrapper')}>
      {courses &&
        courses.length > 0 &&
        courses.map(course => {
          const { assigned_course, statistics } = course

          return (
            <CourseCard
              key={assigned_course.id}
              className={cx('courses_completed')}
              title={assigned_course?.title}
              imageUrl={assigned_course.picture}
              BottomAdornment={() => {
                const isCompleted = statistics?.overall >= 100
                const isTheoryNotExists = isCompleted && statistics.theory === 0
                const isQuizNotExists = isCompleted && statistics.quiz === 0

                return (
                  <CourseStatistics
                    testing={isQuizNotExists ? undefined : statistics.quiz}
                    themes={assigned_course.themes_count}
                    theory={isTheoryNotExists ? undefined : statistics.theory}
                  />
                )
              }}
            />
          )
        })}
    </div>
  )
}

export default MyCourses
