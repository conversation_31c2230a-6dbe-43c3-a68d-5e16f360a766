{"auto_campaigns": "Automatic campaign", "auto_confirm_title": "Do you really want to enable automatic campaign?", "auto_confirm_description": "Within this campaign, all employees will receive a random template at a random time within 2 weeks", "auto_confirm_close": "Cancel", "auto_confirm_confirm": "Enable", "redirect_page": "After entering data, users will be redirected to the page:", "redirect_page_completed": "After entering data, users were redirected to the page:", "redirect_url": "For template '{{template}}' after entering data, redirect is enabled to: {{url}}", "notification": "A message will be sent after the campaign completion to those who:", "notification_completed": "A message will be sent after the campaign completion to those who:", "email_text": "Email text", "post": "Additional phishing campaign will be sent to those who:", "post_completed": "Additional phishing campaign was sent to those who:", "search": "Enter campaign name", "start_date_from": "Start date from", "start_date_to": "Start date to", "end_date_from": "End date from", "end_date_to": "End date to", "incident_percent_from": "Incident level in % from", "incident_percent_to": "Incident level in % to", "targets_from": "Number of employees from", "targets_to": "Number of employees to", "start_date_to_message": "The end date of the start period cannot be earlier than the start date of the period", "end_date_to_message": "The end date of the end period cannot be earlier than the start date of the end period", "incident_percent_to_message": "Incident percentage cannot be less than the minimum value", "targets_to_message": "Number of targets cannot be less than the minimum value", "empty": "No campaigns found with the specified parameters!", "start_date": "Start date", "end_date": "End date", "link_lifetime": "Link lifetime", "targets": "Targets", "indicator": "Incident Level", "select_sort": "Select sorting", "modal_title": "Campaign"}